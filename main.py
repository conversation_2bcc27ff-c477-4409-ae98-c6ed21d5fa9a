import uuid
from langchain_core.messages import HumanMessage
from agent import get_agent, get_tools, model, should_use_search_tool, should_use_date_tool, should_use_slots_tool
from tools import search_database, get_current_date, get_available_slots


def chat_interface():
    """Interactive chat interface for the agentic chatbot"""
    agentic_chatbot = get_agent()
    
    print("=" * 60)
    print("🤖 AGENTIC CUSTOMER SERVICE CHATBOT")
    print("=" * 60)
    print("Available services:")
    print("• Search for products and services")
    print("• Book appointments")
    print("• General customer support")
    print("\nType 'quit' to exit, 'new' to start a new conversation")
    print("=" * 60)
    
    # Create a unique thread for this conversation
    config = {"configurable": {"thread_id": str(uuid.uuid4())}}
    
    while True:
        user_input = input("\n👤 You: ").strip()
        
        if user_input.lower() == 'quit':
            print("\n🤖 Thank you for using our service! Have a great day!")
            break
        elif user_input.lower() == 'new':
            config = {"configurable": {"thread_id": str(uuid.uuid4())}}
            print("\n🤖 Starting a new conversation...")
            continue
        elif not user_input:
            continue
        
        try:
            print("\n🤖 Assistant: ", end="", flush=True)

            # Check if we should force tool usage
            if should_use_search_tool(user_input):
                # Force search tool usage
                print("🔧 Searching database... ")
                search_result = search_database.invoke({"query": user_input})
                print(search_result)
                tool_used = True
            elif should_use_date_tool(user_input) and not should_use_search_tool(user_input):
                # Force date tool usage
                print("🔧 Getting current date... ")
                date_result = get_current_date.invoke({})
                print(date_result)
                tool_used = True
            elif should_use_slots_tool(user_input) and not should_use_search_tool(user_input):
                # Force slots tool usage
                print("🔧 Getting available slots... ")
                slots_result = get_available_slots.invoke({})
                print(slots_result)
                tool_used = True
            else:
                # Send message to the agentic chatbot for simple responses
                user_message = {"messages": [HumanMessage(user_input)]}
                response = agentic_chatbot.invoke(user_message, config=config)

                # Check if there were any tool calls in the conversation
                tool_used = False
                for message in response["messages"]:
                    if hasattr(message, 'tool_calls') and message.tool_calls:
                        tool_used = True
                        print(f"🔧 Used tool: {message.tool_calls[0]['name']} ")

                # Print the final response
                final_message = response["messages"][-1]
                print(final_message.content)

        except Exception as e:
            print(f"\n❌ Error: {str(e)}")
            print("Please try again or contact support.")


def demo_conversation():
    """Run a demo conversation to show the chatbot capabilities"""
    agentic_chatbot = get_agent()
    
    print("\n" + "=" * 60)
    print("🎯 DEMO CONVERSATION")
    print("=" * 60)
    
    demo_inputs = [
        "Hello! I'm looking for a laptop",
        "Can you search for gaming products?",
        "I'd like to book an appointment for tech support",
        "My name is John Smith, <NAME_EMAIL>, phone is 555-0123",
        "I'd like to schedule for tomorrow at 2PM for technical support consultation"
    ]
    
    config = {"configurable": {"thread_id": str(uuid.uuid4())}}
    
    for user_input in demo_inputs:
        print(f"\n👤 User: {user_input}")
        
        try:
            user_message = {"messages": [HumanMessage(user_input)]}
            response = agentic_chatbot.invoke(user_message, config=config)
            print(f"🤖 Assistant: {response['messages'][-1].content}")
            print("-" * 40)
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")


def test_tools():
    """Test the tools directly"""
    tools = get_tools()
    
    print("\n🧪 Testing components...")

    # Test basic model
    print("\n1. Testing basic model:")
    try:
        basic_response = model.invoke("Hello, can you respond?")
        print(f"✅ Model works: {basic_response.content}")
    except Exception as e:
        print(f"❌ Model error: {e}")
        return

    # Test search tool
    print("\n2. Testing search_database tool:")
    try:
        from tools import search_database
        result = search_database("app")
        print(f"✅ Search tool result: {result}")
    except Exception as e:
        print(f"❌ Search tool error: {e}")

    # Test if tools are properly bound to model
    print("\n3. Testing model with tools:")
    try:
        model_with_tools = model.bind_tools(tools)
        response = model_with_tools.invoke("Search for information about apps")
        print(f"✅ Model response: {response.content}")
        if hasattr(response, 'tool_calls') and response.tool_calls:
            print(f"✅ Tool calls detected: {response.tool_calls}")
        else:
            print("⚠️  No tool calls made")
    except Exception as e:
        print(f"❌ Model with tools error: {e}")

    # Test agent
    print("\n4. Testing agent:")
    try:
        agentic_chatbot = get_agent()
        config = {"configurable": {"thread_id": "test-thread"}}
        user_message = {"messages": [HumanMessage("Hello")]}
        response = agentic_chatbot.invoke(user_message, config=config)
        print(f"✅ Agent works: {response['messages'][-1].content}")
    except Exception as e:
        print(f"❌ Agent error: {e}")


if __name__ == "__main__":
    print("Choose an option:")
    print("1. Interactive Chat")
    print("2. Demo Conversation")
    print("3. Test Tools")

    choice = input("Enter your choice (1, 2, or 3): ").strip()

    if choice == "1":
        chat_interface()
    elif choice == "2":
        demo_conversation()
    elif choice == "3":
        test_tools()
    else:
        print("Invalid choice. Starting interactive chat...")
        chat_interface()
