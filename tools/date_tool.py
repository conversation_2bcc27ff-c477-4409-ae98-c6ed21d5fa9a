from datetime import datetime
from langchain_core.tools import tool


@tool
def get_current_date() -> str:
    """
    Get current date and time information.

    Returns:
        Current date and time details
    """
    now = datetime.now()

    return f"""📅 Current Date Information:
• Date: {now.strftime('%Y-%m-%d')}
• Time: {now.strftime('%H:%M:%S')}
• Day: {now.strftime('%A')}
• Formatted: {now.strftime('%B %d, %Y at %I:%M %p')}"""
