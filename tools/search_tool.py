import os
from dotenv import load_dotenv
from langchain_core.tools import tool
from qdrant_client import QdrantClient
from langchain_openai import OpenAIEmbeddings
from langchain_qdrant import QdrantVectorStore, RetrievalMode
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_google_genai import ChatGoogleGenerativeAI

# Load environment variables
load_dotenv()

# Setup embeddings for Qdrant
embeddings = OpenAIEmbeddings(
    model="text-embedding-3-large",
    api_key=os.getenv("OPENAI_API_KEY"),
    dimensions=1536
)

# Initialize Qdrant client
qdrant_client = QdrantClient(
    host="*************",
    port=6333,
)

vector_store = QdrantVectorStore(
    client=qdrant_client,
    collection_name="langsmit_test",
    embedding=embeddings,
    retrieval_mode=RetrievalMode.DENSE,
    content_payload_key="page_content",
    metadata_payload_key="metadata"
)

retriever = vector_store.as_retriever(search_type="mmr", search_kwargs={"k": 5})

llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    temperature=0.1,
    google_api_key=os.getenv("GOOGLE_API_KEY")
)

# Create a proper retrieval chain with prompt
system_prompt = (
    "You are an assistant for question-answering tasks. "
    "Use the following pieces of retrieved context to answer "
    "the question. If you don't know the answer, say that you "
    "don't know. Use three sentences maximum and keep the "
    "answer concise."
    "\n\n"
    "{context}"
)

prompt = ChatPromptTemplate.from_messages([
    ("system", system_prompt),
    ("human", "{input}"),
])

question_answer_chain = create_stuff_documents_chain(llm, prompt)
qa_chain = create_retrieval_chain(retriever, question_answer_chain)


@tool
def search_database(query: str, category: str = "all") -> str:
    """
    Search the database for products or services using Qdrant vector search.

    Args:
        query: The search term to look for
        category: Filter by category (optional, not used in current implementation)

    Returns:
        A formatted string with search results
    """
    try:
        answer = qa_chain.invoke({"input": query})
        result = answer["answer"]
        return result

    except Exception as e:
        return f"Error searching database: {str(e)}"
