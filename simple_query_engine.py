"""
Simple Query Engine - Clean and straightforward implementation.
"""
import os
from dotenv import load_dotenv

# LangChain imports
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import OpenAIEmbeddings
from langchain_qdrant import QdrantVectorStore, RetrievalMode
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.prompts import ChatPromptTemplate

# Qdrant imports
from qdrant_client import QdrantClient

load_dotenv()

class SimpleQueryEngine:
    """Simple, clean query engine."""
    
    def __init__(self):
        self.setup_components()
        self.create_chain()
    
    def setup_components(self):
        """Setup all components."""
        # Embeddings
        self.embeddings = OpenAIEmbeddings(
            model="text-embedding-3-large",
            api_key=os.getenv("OPENAI_API_KEY"),
            dimensions=1536
        )
        
        # Qdrant client
        self.qdrant_client = QdrantClient(
            host="*************",
            port=6333,
        )
        
        # Vector store
        self.vector_store = QdrantVectorStore(
            client=self.qdrant_client,
            collection_name="langsmit_test",
            embedding=self.embeddings,
            retrieval_mode=RetrievalMode.DENSE,
            content_payload_key="page_content",
            metadata_payload_key="metadata"
        )
        
        # Retriever
        self.retriever = self.vector_store.as_retriever(
            search_type="mmr", 
            search_kwargs={"k": 5}
        )
        
        # LLM
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.1,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )
    
    def create_chain(self):
        """Create the retrieval chain."""
        # Simple prompt
        prompt = ChatPromptTemplate.from_template("""
Answer the question based on the provided context:

Context: {context}

Question: {input}

Answer:""")
        
        # Document chain
        document_chain = create_stuff_documents_chain(self.llm, prompt)
        
        # Retrieval chain
        self.qa_chain = create_retrieval_chain(self.retriever, document_chain)
    
    def query(self, question: str) -> str:
        """Simple query method."""
        try:
            result = self.qa_chain.invoke({"input": question})
            return result["answer"]
        except Exception as e:
            return f"Error: {str(e)}"


# Global instance
query_engine = SimpleQueryEngine()

def simple_search(query: str) -> str:
    """Simple search function."""
    return query_engine.query(query)


# Test function
def test_engine():
    """Test the simple query engine."""
    print("🔍 Testing Simple Query Engine")
    print("=" * 40)
    
    test_queries = [
        "What is Ambition Guru?",
        "How to download the app?",
        "What courses are available?",
        "IELTS course details"
    ]
    
    for query in test_queries:
        print(f"\nQ: {query}")
        answer = simple_search(query)
        print(f"A: {answer}")
        print("-" * 40)


if __name__ == "__main__":
    test_engine()
