"""
Enhanced search tool using the advanced retrieval QA system.
"""
from advanced_retrieval_qa import AdvancedRetrievalQA
from typing import Dict, Any
import json


class EnhancedSearchTool:
    """Enhanced search tool with token tracking and better QA."""
    
    def __init__(self):
        self.qa_system = AdvancedRetrievalQA()
        self.query_count = 0
    
    def search_database(self, query: str, category: str = "all") -> str:
        """
        Enhanced search with token tracking and better responses.
        
        Args:
            query: Search query
            category: Category filter (not used in current implementation)
            
        Returns:
            Formatted search results with cost tracking
        """
        self.query_count += 1
        
        try:
            # Use the advanced QA system
            result = self.qa_system.query(query, return_sources=True)
            
            # Format the response
            if result.get('answer') and not result['answer'].startswith('Error:'):
                response = f"🔍 Search Results:\n\n{result['answer']}"
                
                # Add source information if available
                if result.get('sources'):
                    response += f"\n\n📚 Sources ({len(result['sources'])}):"
                    for i, source in enumerate(result['sources'][:3], 1):  # Show top 3 sources
                        source_name = source.get('source', 'Unknown')
                        response += f"\n{i}. {source_name}"
                
                # Add performance info (optional, can be removed for cleaner output)
                response += f"\n\n⚡ Query #{self.query_count} | Time: {result['processing_time']}s | Tokens: {result['token_usage']['total_tokens']} | Cost: ${result['token_usage']['cost']:.6f}"
                
                return response
            else:
                return f"❌ No relevant information found for '{query}'"
                
        except Exception as e:
            return f"❌ Search error: {str(e)}"
    
    def get_usage_summary(self) -> str:
        """Get usage and cost summary."""
        summary = self.qa_system.get_usage_summary()
        
        if summary['runs'] == 0:
            return "📊 No queries processed yet."
        
        return f"""📊 Usage Summary:
• Total Queries: {summary['runs']}
• Total Tokens: {summary['total_tokens']:,}
• Total Cost: ${summary['total_cost']:.6f}
• Average Cost/Query: ${summary['average_cost_per_run']:.6f}
• Prompt Tokens: {summary['total_prompt_tokens']:,}
• Completion Tokens: {summary['total_completion_tokens']:,}"""
    
    def reset_tracking(self):
        """Reset usage tracking."""
        self.qa_system.reset_usage_tracking()
        self.query_count = 0
    
    def batch_search(self, queries: list) -> str:
        """Process multiple queries efficiently."""
        results = self.qa_system.batch_query(queries)
        
        response = f"🔍 Batch Search Results ({len(queries)} queries):\n\n"
        
        for i, (query, result) in enumerate(zip(queries, results), 1):
            response += f"**Query {i}:** {query}\n"
            response += f"**Answer:** {result['answer'][:150]}...\n"
            response += f"**Cost:** ${result['token_usage']['cost']:.6f}\n\n"
        
        # Add summary
        total_cost = sum(r['token_usage']['cost'] for r in results)
        total_tokens = sum(r['token_usage']['total_tokens'] for r in results)
        
        response += f"📊 Batch Summary: {len(queries)} queries, {total_tokens} tokens, ${total_cost:.6f} total cost"
        
        return response


# Global instance for use in agentic system
enhanced_search = EnhancedSearchTool()


def search_database_enhanced(query: str, category: str = "all") -> str:
    """
    Enhanced search function for use in agentic chat system.
    Drop-in replacement for the original search_database function.
    """
    return enhanced_search.search_database(query, category)


def get_search_usage() -> str:
    """Get search usage summary."""
    return enhanced_search.get_usage_summary()


def reset_search_tracking() -> str:
    """Reset search tracking."""
    enhanced_search.reset_tracking()
    return "✅ Search tracking reset successfully."


# Example usage and testing
def test_enhanced_search():
    """Test the enhanced search functionality."""
    print("🧪 Testing Enhanced Search Tool")
    print("=" * 50)
    
    # Test single queries
    test_queries = [
        "What is Ambition Guru?",
        "How to download the app?",
        "IELTS course information",
        "Loksewa preparation details"
    ]
    
    print("\n📝 Single Query Tests:")
    for i, query in enumerate(test_queries, 1):
        print(f"\n--- Test {i} ---")
        print(f"Query: {query}")
        result = enhanced_search.search_database(query)
        print(f"Result: {result[:200]}...")
    
    print(f"\n{enhanced_search.get_usage_summary()}")
    
    # Test batch processing
    print(f"\n📦 Batch Processing Test:")
    batch_queries = [
        "What courses are available?",
        "How much does CSIT cost?",
        "What documents are needed for BBS?"
    ]
    
    batch_result = enhanced_search.batch_search(batch_queries)
    print(batch_result)
    
    print(f"\n{enhanced_search.get_usage_summary()}")


if __name__ == "__main__":
    test_enhanced_search()
